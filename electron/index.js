// // 导入模块
// const { app, BrowserWindow ,ipcMain, Tray, Menu,nativeImage ,Notification,screen,dialog,shell,desktopCapturer,webContents} = require('electron')
// const { autoUpdater } = require("electron-updater");
// const log = require('electron-log');

// log.transports.console = true; // 启用控制台输出
// log.transports.file.level = 'info'; // 设置日志级别为info
// log.transports.console.level = 'debug'; // 设置控制台输出的日志级别为debug
// log.format = '{h}:{i}:{s} {text}';
// log.info('11');
// log.debug('更新可用');
// const path = require('path')
// const url = require('url');
// // let version = app.getVersion(); //获取版本号
// // console.log(version,'8975');
// const NODE_ENV = process.env.NODE_ENV
// let mainWindow,privacyWindow,progressWindow
// let customNoticeWindow
// let tray = null
// let timer = null
// let isMaximizedFlag= true
// // 创建主窗口
// const createWindow = () => {
//    mainWindow = new BrowserWindow({
//     width: 1027,
//     height: 600,
//     autoHideMenuBar: true,
//     frame: false, //隐藏默认最小化、最大化、退出工具栏
//     // titleBarStyle: 'hidden',
//     // titleBarStyle: 'customButtonsOnHover',
//     // titleBarStyle: 'hidden',
//     // titleBarOverlay: {
//     //   color: '#2f3241',
//     //   symbolColor: '#74b1be',
//     // },
//     webPreferences: {
//       nodeIntegration:true,
//       contextIsolation:false,
//       webSecurity:false, //跨域禁用
//       standardMenu:false,
//       session:{
//         cookies:true
//       },
//       preload: path.join(__dirname, './preload.js'),
//     },
//   })

//   // mainWindow.setBackgroundColor('#333333')
//   mainWindow.setMinimumSize(1027, 600); //限制窗口缩小宽为900,高为600
//   // // mainWindow.setTitleBarColor('#0069b4');
//   // mainWindow.setTitleBarOverlay({
//   //   color: '#333333', // 这里可以设置你想要的颜色
//   //   height: 30 // 设置标题栏的高度
//   // })
//   // 和自己本地vue项目启动的地址保持一致
//   // mainWindow.loadURL('http://localhost:80/')
//   if (NODE_ENV === "development") {
//     mainWindow.loadURL(NODE_ENV === 'development'?'http://localhost:80/' :`file://${path.join(__dirname, '../index.html')}`)
//     mainWindow.webContents.openDevTools() //打开控制台
//   }
//   else{
//     mainWindow.loadFile(path.join(__dirname,  '../dist-prod/index.html'))
//     // mainWindow.loadFile(('../dist-prod/index.html'))
//     // mainWindow.loadFile(('./dist/index.html'))
//     // mainWindow.loadFile(('../index.html'))
//     // mainWindow.loadFile((`file://${path.join(__dirname, '../dist-prod/index.html')}`))
//     // mainWindow.webContents.openDevTools() //打开控制台

//   }
//     //创建系统通知区菜单
//     tray = new Tray(path.join(__dirname,NODE_ENV === 'development'? '../public/buu.ico' : '../dist-prod/buu.ico'))
//     //定制退出操作
//     const contextMenu = Menu.buildFromTemplate([
//       { label: '退出', click: () => { mainWindow.destroy() } },
//       // { label: 'Show Notification', click: () => mainWindow.webContents.send('notification-shown') }
//     ])
//     tray.setToolTip('办办')
//     tray.setContextMenu(contextMenu)
//     // tray.on('click', () => {
//     //   mainWindow.show()
//     //   mainWindow.setSkipTaskbar(false)
//     // })
//     // 消息通知，利用计时器，通过Image实例和空的Image实例 来实现图标闪动效果；
//     let msgFlag = false
//     ipcMain.on('have-message', (event, arg) => {
//       mainWindow.flashFrame(true);// 当收到消息时，窗口在任务栏高亮
//       // return
//       if(timer){
//         clearInterval(timer)
//       }
//       timer = setInterval(() => {
//         msgFlag = !msgFlag
//         msgFlag ? tray.setImage(nativeImage.createEmpty()) : tray.setImage(path.join(__dirname,NODE_ENV === 'development'? '../public/buu.ico' : '../dist-prod/buu.ico'))
//         tray.setToolTip('您有一条新消息')
//       }, 500)
//     })
//     // 监听主窗口是否打开
//     mainWindow.on('focus', () => {
//       // 在这里执行你想要的操作
//       console.log('Window is focused.');
//       if(timer){
//         clearInterval(timer)
//       }
//       timer = null
//       msgFlag = false
//       tray.setToolTip('办办')
//       tray.setImage(path.join(__dirname,NODE_ENV === 'development'? '../public/buu.ico' : '../dist-prod/buu.ico'))// 防止 mainWindow.show() 后系统托盘图标的 空白现象；
//     });
//     tray.on('click', (_event, _bounds, _position) => { // 单击该系统托盘图标，显示隐藏窗口
//           clearInterval(timer)
//           timer = null
//           msgFlag = false
//           tray.setToolTip('办办')

//           tray.setImage(path.join(__dirname,NODE_ENV === 'development'? '../public/buu.ico' : '../dist-prod/buu.ico'))// 防止 mainWindow.show() 后系统托盘图标的 空白现象；
//           if (mainWindow.isVisible()) {
//             // mainWindow.hide()
//             mainWindow.show()
//             mainWindow.setSkipTaskbar(false) //窗口是否出现在任务栏上
//           } else {
//             mainWindow.setSkipTaskbar(false)
//             mainWindow.show()
//             if (!isMaximizedFlag) {
//               mainWindow.maximize(); // 如果窗口最大化，则窗口最大化
//             }else{
//               mainWindow.unmaximize(); // 如果窗口最小化，则最小化
//             }
//           }
         
//       })
//   // 示例代码
//     // ipcMain.on('have-message', (event, messageData) => {
//     //   // 如果窗口没有焦点，则闪烁窗口
//     //   if (!mainWindow.isFocused()) {
//     //     mainWindow.flashFrame(true);
//     //   }
   
//     //   // 在系统通知区域显示消息
//     //   new Notification({
//     //     title: '消息来自',
//     //     subtitle: messageData, // 消息来源者的名字
//     //     body: messageData, // 消息内容
//     //     icon: 'path-to-icon/icon.png' // 消息图标的路径
//     //   });
//     // });
//       // 示例代码结尾
//       // showFloatingWindow() 旧的改动需要放开
  
//       autoUpdater.autoDownload = false
// // 在初始化时检查更新
// // createProgressWindow()

// setTimeout(() => {
//   // autoUpdater.checkForUpdatesAndNotify();
//   autoUpdater.checkForUpdates();
// }, 1000);

// }

// autoUpdater.on('update-available', async () => {
//   console.log(1);
//   // autoUpdater.downloadUpdate()
  
//   // mainWindow.webContents.openDevTools() //打开控制台
//   console.log('更新可用');
//   console.log('11');
//   const result = await dialog.showMessageBox({
//     type: 'info',
//     title: '发现新版本',
//     message: '发现新版本，是否立即更新？',
//     // buttons: ['现在更新', '稍后']
//     buttons: ['稍后', '现在更新']
// });
// if (result.response === 1) {
//   autoUpdater.downloadUpdate()
// }
//   // mainWindow.webContents.send('update-message', '发现新版本，准备下载...');


// });



// autoUpdater.on('update-not-available', () => {
//   console.log('更新不可用');
//   console.log('221');
// });

// autoUpdater.on('error', (error) => {
//   console.log(`更新错误: ${error}`);
//   console.log('33');
// });
// autoUpdater.on('download-progress', (progressObj) => {
//   // console.log(`下载进度: ${progressObj.percent}`);
//   // mainWindow.setProgressBar(progressObj.percent / 100);
//   // let logMessage = '下载监听：' + progressObj.percent.toFixed(2) + '%'
//   // console.log(logMessage)
//   // progressWindow.webContents.send('update-progress',progressObj);
//   // progressWindow.webContents.send('update-progress','hahah');
//   // mainWindow.webContents.send('update-progress','hahah');

//   console.log('44');
// });

// autoUpdater.on('update-downloaded', () => {
//   // progressWindow.webContents.send('update-progress','hahah');
//   // mainWindow.webContents.send('update-progress','hahah');
//   // return
//   console.log('更新下载完成，将重启应用程序以应用更新');
//   // 通常你会想要在这里提示用户应用程序将重启
//   // 重启应用程序
//   dialog.showMessageBox({
//     type: 'info',
//     title: '更新下载完成',
//     // message: '更新已下载完成，即将退出安装。',
//     message: '最新版本已下载完成，请重启更新。',
//     buttons: ['确定']
// }, () => {
//     setImmediate(() => {
//       autoUpdater.quitAndInstall(); // 退出安装
//     });
// });
//   // autoUpdater.quitAndInstall();
//   console.log('55');
// });




// const selfWindws = async () =>
//   await Promise.all(
//     webContents
//       .getAllWebContents()
//       .filter((item) => {
//         const win = BrowserWindow.fromWebContents(item);
//         return win && win.isVisible();
//       })
//       .map(async (item) => {
//         const win = BrowserWindow.fromWebContents(item);
//         const thumbnail = await win?.capturePage();
//         // 当程序窗口打开DevTool的时候  也会计入
//         return {
//           name:
//             win?.getTitle() + (item.devToolsWebContents === null ? "" : "-dev"), // 给dev窗口加上后缀
//           id: win?.getMediaSourceId(),
//           thumbnail,
//           display_id: "",
//           appIcon: null,
//         };
//       })
//   );

// // 获取设备窗口信息
// ipcMain.handle("ev:send-desktop-capturer_source", async (_event, _args) => {
//   return [
//     ...(await desktopCapturer.getSources({ types: ["window", "screen"] })),
//     ...(await selfWindws()),
//   ];
// });


// function showFloatingWindow() {
// 	//鼠标经过托盘图标时，出现自定义系统通知窗口
// 	if(process.platform !== 'darwin'){ //非mac系统时出现
// 		const appTrayBounds = tray.getBounds();//获取系统托盘所在位置
// 		createCustomNoticeWindow({x: appTrayBounds.x, y: appTrayBounds.y})//创建 鼠标经过托盘图标时出现的自定义系统通知窗口
// 		let isLeaveTray = true;//存储鼠标是否离开托盘的状态
// 		let isLeaveTimer = null;
// 		tray.on('mouse-move',() => {//系统托盘鼠标经过时触发
// 			const appTrayBounds = tray.getBounds();//获取系统托盘所在位置
// 			let params = {}
// 			if(isLeaveTray){
// 				if(!params.x) {
// 					params.x = appTrayBounds.x - (220/2);
// 				}
// 				if(!params.y) {
// 					params.y = appTrayBounds.y - customNoticeWindow.getBounds().height;
// 				}
// 				if(params.x < 0){
// 					params.x = screen.getPrimaryDisplay().bounds.width - params.x
// 				}
// 				if(params.y < 0){
// 					params.y = screen.getPrimaryDisplay().bounds.height - params.y
// 				}
// 				customNoticeWindow.setBounds(params);
// 				customNoticeWindow.show()//显示自定义系统通知窗口
// 			}
// 			isLeaveTray = false;
		
// 			//检查鼠标是否从托盘离开
// 			clearInterval(isLeaveTimer)
// 			isLeaveTimer = setInterval(() => {
// 				let point = screen.getCursorScreenPoint();
// 				// 判断鼠标是否再托盘内
// 				if(!(appTrayBounds.x < point.x && appTrayBounds.y < point.y && point.x < (appTrayBounds.x + appTrayBounds.width) && point.y < (appTrayBounds.y  + appTrayBounds.height))){
// 					 // 判断鼠标是否在弹出菜单内
// 					 let menuBounds = customNoticeWindow?.getBounds()
// 					 if(menuBounds && menuBounds.x < point.x && menuBounds.y < point.y && point.x < (menuBounds.x + menuBounds.width) && point.y < (menuBounds.y  + menuBounds.height)) {
// 						 console.log('鼠标在新消息菜单内');
// 						 return ;
// 					 }
// 					 // 触发 mouse-leave
// 					 clearInterval(isLeaveTimer);
// 					 customNoticeWindow.hide(); // 隐藏自定义系统通知窗口
// 					 isLeaveTray = true;
// 					 console.log("鼠标离开系统托盘图标")
// 				} else {
// 					console.log('鼠标在系统托盘图标内');
// 				}
// 			}, 100)
		
// 		});
// 	}

//   // // if (!floatingWindow) {
//   //   floatingWindow = new BrowserWindow({
//   //     width: 200,
//   //     height: 100,
//   //     frame: false, // 无边框
//   //     alwaysOnTop: true, // 置顶
//   //     showInTaskbar: true, // 不显示在任务栏
//   //     // show: false, // 初始不显示
//   //     // webPreferences: {
//   //     //   nodeIntegration: true
//   //     // }
//   //   });
//   //   // floatingWindow.setSkipTaskbar(true)

//   //   floatingWindow.loadFile(path.join(__dirname,'./aa.vue'))
//   //   floatingWindow.once('ready-to-show', () => {
//   //     floatingWindow.show();
//   //   });
  
// }
// /**
//  * 创建自定义系统通知窗口
//  */
// function createCustomNoticeWindow(options) {
// 	customNoticeWindow = new BrowserWindow(Object.assign({
// 		width: 220,
// 		minHeight: 120,
// 		height: 170,
// 		frame: false,// 无边框
// 		show: false,
// 		modal: true,
// 		parent: mainWindow,//指定父窗口
// 		icon: path.join(__dirname,NODE_ENV === 'development'? '../public/buu.ico' : '../dist-prod/buu.ico'),// 窗口图标
// 		disableAutoHideCursor: true,// 是否在打字时隐藏光标
// 		resizable: false,//窗口大小是否可调整
// 		movable: false,//窗口是否可移动
// 		alwaysOnTop: true,// 窗口是否永远在别的窗口的上面
// 		fullscreenable: false,//窗口是否可以进入全屏状态
// 		webPreferences: {//网页功能设置。
//       preload: path.join(__dirname, './preload.js'),//在页面运行其他脚本之前预先加载指定的脚本 无论页面是否集成Node, 此脚本都可以访问所有Node API 脚本路径为文件的绝对路径。
//       webSecurity: false,//禁用同源策略
// 			plugins:true,//是否应该启用插件
// 		},
// 	},options))
// 	customNoticeWindow.loadURL(path.join(__dirname,"./customNoticeHtml.html")); // 加载对应的菜单栏页面
// 	// customNoticeWindow.loadFile(path.join(__dirname,"./aa.vue")); // 加载对应的菜单栏页面

// 	//监听到主窗口关闭则清空
// 	customNoticeWindow.on('closed',() => {
// 		customNoticeWindow = null;
// 	})
// }

// /**
//  * 创建自定义进度条窗口
//  */
// function createProgressWindow() {
//  progressWindow = new BrowserWindow({
// 		width: 400,
// 		// minHeight: 120,
// 		height:160,
// 		frame: false,// 无边框
// 		resizable: false,//窗口大小是否可调整
// 		movable: false,//窗口是否可移动
// 		alwaysOnTop: true,// 窗口是否永远在别的窗口的上面
// 		fullscreenable: false,//窗口是否可以进入全屏状态
// 		webPreferences: {//网页功能设置。
//       preload: path.join(__dirname, './preload.js'),//在页面运行其他脚本之前预先加载指定的脚本 无论页面是否集成Node, 此脚本都可以访问所有Node API 脚本路径为文件的绝对路径。
//       webSecurity: false,//禁用同源策略
// 			plugins:true,//是否应该启用插件
//       contextIsolation:false,
// 		},
// 	})
//   const winURL =  NODE_ENV === 'development' ? `http://localhost:80` : url.format({
//     pathname: path.join(__dirname, '../dist-prod/index.html'),
//     protocol: 'file:',
//     slashes: true
//   });
//   // progressWindow.webContents.openDevTools() //打开控制台
//   progressWindow.loadURL(winURL+'#/progresse');
//   // progressWindow.webContents.send('update-progress','hahah');

// 	//监听到主窗口关闭则清空
// 	progressWindow.on('closed',() => {
// 		progressWindow = null;
// 	})
// }


//   // 示例代码
// 	// // 系统托盘
//   // var appIcon = null

//   // function implementSystemTray() {
//   //   appIcon = new Tray('./public/favicon.ico') // 创建与image关联的图标
//   //   var contextMenu = Menu.buildFromTemplate([ // 创建菜单
//   //     {
//   //       label: '上一首',
//   //       type: 'radio',
//   //       click: function() {
//   //         console.log('播放上一首歌曲')
//   //       }
//   //     },
//   //     { label: '下一首', type: 'radio' },
//   //     { label: '打开桌面歌词', type: 'radio', checked: true },
//   //     { label: '退出', role: 'close', accelerator: 'CmdOrCtrl+Q' }
//   //   ])
//   //   appIcon.setToolTip('This is demo') // 鼠标放在该系统托盘图标上的提示信息
//   //   appIcon.setContextMenu(contextMenu)
  
//   //   // 消息通知，利用计时器，通过Image实例和空的Image实例 来实现图标闪动效果；
//   //   let timer = null
//   //   let msgFlag = false
//   //   ipcMain.on('have-message', (event, arg) => {
//   //     timer = setInterval(() => {
//   //       msgFlag = !msgFlag
//   //       msgFlag ? appIcon.setImage(nativeImage.createEmpty()) : appIcon.setImage('./public/favicon.ico')
//   //       appIcon.setToolTip('您有一条新消息')
//   //     }, 500)
//   //   })
  
//   //   appIcon.on('click', (_event, _bounds, _position) => { // 单击该系统托盘图标，显示隐藏窗口
//   //     if (win.isVisible()) {
//   //       win.hide()
//   //     } else {
//   //       win.show()
//   //       appIcon.setImage('./public/favicon.ico') // 防止 win.show() 后系统托盘图标的 空白现象；
//   //       clearInterval(timer)
//   //       timer = null
//   //       msgFlag = false
//   //     }
//   //   })
//   // }
//   //  // electron完成初始化的时候触发
//   //    app.on('ready', async() => {
//   //         implementSystemTray();
//   //     })



//   // 创建一个设置与隐私的新窗口
//   function popUpNotification(){
//     if(privacyWindow==null){
//       privacyWindow = new BrowserWindow({
//         width: 680,
//         height: 560,
//         frame: false, //隐藏默认最小化、最大化、退出工具栏
//       // parent: mainWindow,//指定父窗口
//         webPreferences: {
//           nodeIntegration: true, // 如果你需要在渲染进程中使用Node.js
//           contextIsolation:false,
//           preload: path.join(__dirname, './preload.js'),
//           webSecurity: false,//禁用同源策略
//         },
//         resizable: false // 设置窗口不可调整大小
//       });
//       const winURL =  NODE_ENV === 'development' ? `http://localhost:80` : url.format({
//        pathname: path.join(__dirname, '../dist-prod/index.html'),
//        protocol: 'file:',
//        slashes: true
//      });
//     //  privacyWindow.webContents.openDevTools() //打开控制台
//      privacyWindow.loadURL(winURL+'#/settingsAndPrivacy');
//      privacyWindow.on('closed', () => {
//         privacyWindow = null;
//       });
//     }
   
//   }
 


// //   function screenCapture  () {
// //     console.log(111);
    
// //     //定义屏幕大小
// //     const getSize = () => {
// //         const { size, scaleFactor } = screen.getPrimaryDisplay();
// //         return {
// //             width: size.width * scaleFactor,
// //             height: size.height * scaleFactor
// //         }
// //     }
// //     const sizeInfo = getSize();
// //     desktopCapturer.getSources({
// //         types: ['window','screen'], // 设定需要捕获的是"屏幕"，还是"窗口"
// //         thumbnailSize: sizeInfo
// //     }).then(async sources => {
// //         //获取第一个屏幕
// //         let imageData = sources[0].thumbnail.toDataURL("image/png");
// //         console.log(imageData);
        
// //         //将base64 流发送到渲染进程 
// //         // BrowserWindow.getFocusedWindow().webContents.send("source", imageData);
// //     })
// // }

                        




// // 应用准备就绪，加载窗口
// app.whenReady().then(() => {
//   // 监听消息在当前聊天框取消图标闪烁
//   ipcMain.on('cancel-message', (event, arg) => {
//     mainWindow.flashFrame(false);// 当前聊天框接收新消息时，取消窗口在任务栏高亮
//     if(timer){
//       clearInterval(timer)
//       timer = null
//       msgFlag = false
//       tray.setToolTip('办办')
//       tray.setImage(path.join(__dirname,NODE_ENV === 'development'? '../public/buu.ico' : '../dist-prod/buu.ico'))// 防止 mainWindow.show() 后系统托盘图标的 空白现象；
//     }
   
//   })
//   // shell.openExternal(val); 调起浏览器下载文件
// // 监听打开新窗口window.open打开链接预览
// ipcMain.on('openWindow',  (_,val)=> {
//    // 创建一个新的浏览器窗口
//   const  win = new BrowserWindow({
//     width: 800,
//     height: 600,
//     autoHideMenuBar:true, // 隐藏默认的File Edit View  Window  Help菜单栏
//     webPreferences: {
//       nodeIntegration: true, // 如果你需要在渲染进程中使用Node.js
//     },
//   });
//   // 加载内容
//   win.loadURL(val);
// })
// //  监听创建一个设置与隐私的新窗口
// ipcMain.on('popUpNotification',  (_,val)=> {
//   popUpNotification()
//   // screenCapture()
// })

// //  监听更新提示和进度条的窗口 关闭
// ipcMain.on('window-progress',  (_,val)=> {
//   progressWindow.destroy()

// })
// //  监听更新提示和进度条的窗口 是否更新
// ipcMain.on('window-downloadUpdate',  (_,val)=> {
//   console.log('触发');
//   autoUpdater.checkForUpdates();
// })


//   // 监听主窗口缩小化
//   ipcMain.on('window-min', (_,val) => {
//     mainWindow.minimize()
//   })
//     // 监听设置与隐私的窗口缩小化
// ipcMain.on('window-min1', (_,val) => {

//   privacyWindow.minimize()
// })
//     // 监听设置与隐私的窗口关闭
// ipcMain.on('window-close1', (event) => {
//   privacyWindow.destroy()
// })
// // 监听主窗口的关闭隐藏到托盘
// ipcMain.on('window-close', (event) => {
//   mainWindow.hide();
//   mainWindow.setSkipTaskbar(true);
//   event.preventDefault();
// })
// //监听主窗口 最大化和恢复最大化之前的窗口大小
// ipcMain.on('maximize', (_,val) => {
//   // mainWindow.isMaximized() ? mainWindow.unmaximize() : mainWindow.maximize();
//   if(mainWindow.isMaximized()){
//     mainWindow.unmaximize() //取消最大化
//     isMaximizedFlag = true
//   }else{
//     mainWindow.maximize() //最大化
//     isMaximizedFlag = false 
//   }
// })
// // 监听主窗口控制台连续点击五下打开
// ipcMain.on('openDevtools', (_,val) => {
//   mainWindow.webContents.openDevTools() //打开控制台
//   console.log(1);
  
// })

// // 监听事件
// ipcMain.on('show-notification', (_,message) => {
//   // new Notification({ title: 'Notification', body: 'This is a notification' });
//   const myNotification = new Notification({ title: 'Notification', body: message });
//   myNotification.show();
// });
// // 确保应用的单实例
// const gotTheLock = app.requestSingleInstanceLock();
// if (!gotTheLock) {
//   app.quit();
// } else {
//   app.on('second-instance', (event, commandLine, workingDirectory) => {
//     // 当启动第二个实例时，将焦点放到主窗口
//     if (mainWindow) {
//       if (mainWindow.isMinimized()){
//         mainWindow.restore();
//         mainWindow.focus();
//         // mainWindow.show()
//         // mainWindow.setSkipTaskbar(false)
//       } 
//     }
//   });
//   createWindow()
// }

//   // createWindow()
//   // mac 上默认保留一个窗口
//   app.on('activate', () => {
//     if (BrowserWindow.getAllWindows().length === 0) createWindow()
//   })

// // 监听键盘事件Alt键 防止隐藏后再按Alt键默认的File Edit View  Window  Help菜单栏又显示出来
//   app.on('web-contents-created', (event, webContents) => {
//     webContents.on('before-input-event', (event, input) => {
//       if (input.alt && input.type === 'keyDown') {
//         // 隐藏默认的File Edit View  Window  Help菜单栏
//         Menu.setApplicationMenu(null);
//       }
//     });
//   });
// })
// // 关闭所有窗口 ： 程序退出 ： windows & linux
// app.on('window-all-closed', () => {
//   if (process.platform !== 'darwin') app.quit()
// })