interface Member {
  name: string;
  avatar: string;
}

// 公共常量
const CANVAS_SIZE = 240;
const BLUE_COLOR = '#3370FF';
const WHITE_COLOR = '#FFFFFF';
const RADIUS_RATIO = 0.1; // 圆角比例
const FONT_SIZE_RATIO = 0.4; // 字体大小比例

/**
 * 绘制圆角矩形路径
 */
function drawRoundedRect(ctx: CanvasRenderingContext2D, x: number, y: number, size: number) {
  const radius = size * RADIUS_RATIO;
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + size - radius, y);
  ctx.quadraticCurveTo(x + size, y, x + size, y + radius);
  ctx.lineTo(x + size, y + size - radius);
  ctx.quadraticCurveTo(x + size, y + size, x + size - radius, y + size);
  ctx.lineTo(x + radius, y + size);
  ctx.quadraticCurveTo(x, y + size, x, y + size - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
}

/**
 * 绘制文字头像
 */
function drawTextAvatar(ctx: CanvasRenderingContext2D, name: string, x: number, y: number, size: number) {
  ctx.save();
  drawRoundedRect(ctx, x, y, size);
  ctx.fillStyle = BLUE_COLOR;
  ctx.fill();

  const text = name.slice(-2);
  ctx.fillStyle = WHITE_COLOR;
  ctx.font = `${size * FONT_SIZE_RATIO}px Arial`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(text, x + size / 2, y + size / 2);
  ctx.restore();
}

/**
 * 绘制图片头像
 */
function drawRoundedAvatar(ctx: CanvasRenderingContext2D, img: HTMLImageElement, x: number, y: number, size: number) {
  ctx.save();
  drawRoundedRect(ctx, x, y, size);
  ctx.closePath();
  ctx.clip();
  ctx.drawImage(img, x, y, size, size);
  ctx.restore();
}

/**
 * 获取布局信息
 */
function getLayoutInfo(count: number) {
  const layouts = {
    1: { rows: [1], itemSize: 0.98 },
    2: { rows: [2], itemSize: 0.47 },
    3: { rows: [1, 2], itemSize: 0.47 },
    4: { rows: [2, 2], itemSize: 0.47 },
    5: { rows: [2, 3], itemSize: 0.32 },
    6: { rows: [3, 3], itemSize: 0.32 },
    7: { rows: [1, 3, 3], itemSize: 0.32 },
    8: { rows: [2, 3, 3], itemSize: 0.32 },
    9: { rows: [3, 3, 3], itemSize: 0.32 }
  };
  return layouts[Math.min(count, 9) as keyof typeof layouts];
}

/**
 * 获取头像位置
 */
function getAvatarPosition(index: number, total: number) {
  const layout = getLayoutInfo(total);
  const size = CANVAS_SIZE * layout.itemSize;
  const margin = CANVAS_SIZE * 0.01;

  let currentRow = 0;
  let currentIndex = index;

  for (let i = 0; i < layout.rows.length && currentIndex >= layout.rows[i]; i++) {
    currentIndex -= layout.rows[i];
    currentRow++;
  }

  const rowCount = layout.rows[currentRow];
  const rowWidth = rowCount * size + (rowCount - 1) * margin;
  const startX = (CANVAS_SIZE - rowWidth) / 2;

  let startY = currentRow * (size + margin);
  if (total === 2) {
    startY = (CANVAS_SIZE - size) / 2
  } else if (total === 5 || total === 6) {
    const totalHeight = 2 * size + margin;
    startY = (CANVAS_SIZE - totalHeight) / 2 + currentRow * (size + margin);
  }

  return { x: startX + currentIndex * (size + margin), y: startY, size };
}

/**
 * 生成群聊头像，使用方法：
  ```
     const members = [
          { name: '小白菜', avatar: "https://oss-banban.hqbang.cn/test/a4fe48c534e014187bbbaddd05cc5d37ef1ca8fb2fbbb5aca0d3ebe2ac82eb01.png"
          },
          { name: '大白菜', avatar: '' },
          { name: '小黄鱼', avatar: '' },
          { name: '大黄鱼', avatar: '' },
          { name: '小鲤鱼', avatar: '' },

        ];

  generateGroupAvatar(members).then(dataUrl => {
     console.log(dataUrl)
   });
 *
 * ```
 */
export function generateGroupAvatar(members: Member[]): Promise<string> {
  return new Promise((resolve) => {
    const displayMembers = members.slice(0, 9);
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;

    canvas.width = canvas.height = CANVAS_SIZE;
    ctx.fillStyle = WHITE_COLOR;
    ctx.fillRect(0, 0, CANVAS_SIZE, CANVAS_SIZE);

    let loadedCount = 0;
    const checkComplete = () => {
      if (++loadedCount === displayMembers.length) {
        resolve(canvas.toDataURL('image/png'));
      }
    };

    displayMembers.forEach((member, index) => {
      const position = getAvatarPosition(index, displayMembers.length);

      if (member.avatar) {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = () => {
          drawRoundedAvatar(ctx, img, position.x, position.y, position.size);
          checkComplete();
        };
        img.onerror = () => {
          drawTextAvatar(ctx, member.name, position.x, position.y, position.size);
          checkComplete();
        };
        img.src = member.avatar;
      } else {
        drawTextAvatar(ctx, member.name, position.x, position.y, position.size);
        checkComplete();
      }
    });
  });
}

/**
 * 生成单个文字头像
 * @param name 用户名
 * @param size 头像尺寸，默认 60px
 * @returns base64格式的图片
 * @example
  // 使用默认尺寸（60px）
  const avatar1 = generateTextAvatar('张三丰');

  // 指定尺寸
  const avatar2 = generateTextAvatar('李四', 100);

  // 在 img 标签中使用
  const img = new Image();
  img.src = generateTextAvatar('王五');
  document.body.appendChild(img);
 */
export function generateTextAvatar(name: string, size: number = 60): string {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d')!;

  canvas.width = canvas.height = size;
  drawTextAvatar(ctx, name, 0, 0, size);

  return canvas.toDataURL('image/png');
}
