
import { ref } from 'vue';
import { getAccessToken } from '@/utils/auth'

export function useOriginUrl() {

  const originUrl = ref<string>(window.location.origin)
  // const originUrl = ref<string>('https://oa.nxbanban.com')




  // 根据不同条件设置 originUrl
  if (window.location.origin.includes("*************:8029")) {
    originUrl.value = 'http://************:48080';
  } else if (window.location.origin.includes("*************:8028") || window.location.origin.includes("test.dinggehuo.com")) {
    originUrl.value = 'https://test.dinggehuo.com/banban'; // 测试环境ip
  } else if (window.location.origin.includes("localhost") || window.location.origin.includes("************")) {
    originUrl.value = 'https://test.dinggehuo.com/banban'; // 自用
  } else if (window.location.origin.includes("nxbanban.com")) {
    originUrl.value = 'https://api.nxbanban.com/banban'; // 生产
  }

  // console.log('originUrl', originUrl.value)
  // console.log('window.location.origin', window.location.origin)

  const token = getAccessToken()
  const server = ref<string>((originUrl.value + '/infra/ws').replace('http', 'ws').replace('https', 'wss') + '?token=' + token)

  return { originUrl, server }
}
