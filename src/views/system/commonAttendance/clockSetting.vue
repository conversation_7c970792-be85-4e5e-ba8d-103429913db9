<template>
  <div class="app-main">

    <div>
      <el-form :inline="true" ref="queryFormRef" :model="queryParams" @keydown.enter.prevent>
        <el-form-item label="打卡名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="打卡名称" class="!w-220px"></el-input>
        </el-form-item>

        <!-- <el-form-item label="考勤类型" prop="type">
          <el-select v-model="queryParams.type" class="!w-220px" clearable>
            <el-option label="考勤班次管理" :value="1" />
            <el-option label="算薪考勤管理" :value="2" />
          </el-select>
        </el-form-item> -->

        <el-form-item>
          <el-button @click="handleSearch" type="primary">
            搜索
          </el-button>
          <el-button @click="resetQuery">
            重置
          </el-button>
        </el-form-item>

      </el-form>
    </div>
    <div class="option-btns">
      <el-button type="primary" @click="handleEdit()">新增打卡时间组</el-button>
    </div>
    <div>
      <el-table :data="list">
        <el-table-column label="序号" type="index" width="80"></el-table-column>
        <el-table-column label="打卡组名称" prop="name"></el-table-column>





        <el-table-column label="操作">
          <template v-slot="{ row }">
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" @click="handleConfig(row)">配置</el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
        @pagination="getList" />

    </div>


    <el-dialog :title="title" v-model="editVisible" width="1200" top="20vh">
      <el-form :model="editForm" ref="editFormRef" :rules="rules" label-position="left" label-width="120">
        <el-form-item label="打卡组名称:" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入打卡组名称" class="input-box"></el-input>
        </el-form-item>
      </el-form>

      <div style="margin-bottom:20px;" v-if="editForm.shiftTimes.length < 8">
        <el-button type="primary" @click="shiftTimesAdd()">增加</el-button>

      </div>

      <el-table :data="editForm.shiftTimes">

        <el-table-column label="工作日时间">
          <template v-slot="{ row }">
            <el-time-picker value-format="HH:mm:ss" format="HH:mm" v-model="row.startTime" placeholder="请选择时间" />
          </template>
        </el-table-column>
        <el-table-column label="打卡有效时间段">
          <template v-slot="{ row }">
            <el-time-picker is-range value-format="HH:mm" format="HH:mm" v-model="row.efficientTimeRange"
              range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间"
              @change="handleTimeRangeChange(row)" />
          </template>
        </el-table-column>

        <!-- <el-table-column label="打卡开始时间">
          <template v-slot="{ row }">
            <el-time-picker value-format="HH:mm" format="HH:mm" v-model="row.efficientStratTime" placeholder="请选择时间" />
          </template>
        </el-table-column>
        <el-table-column label="打卡结束时间">
          <template v-slot="{ row }">
            <el-time-picker value-format="HH:mm" format="HH:mm" v-model="row.efficientEndTime" placeholder="请选择时间" />
          </template>
        </el-table-column> -->
        <el-table-column label="打卡内容" prop="description" width="220"></el-table-column>

        <el-table-column label="操作" width="120">
          <template v-slot="scope">

            <el-button v-if="scope.$index === editForm.shiftTimes.length - 1" type="text"
              @click="editForm.shiftTimes.pop()">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>




      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editVisible = false">取消</el-button>
          <el-button type="primary" @click="editSubmit()">
            确认
          </el-button>
        </div>
      </template>

    </el-dialog>



    


    <configTreeView ref="configTree"></configTreeView>
  </div>
</template>
<script lang='ts' setup>
import configTreeView from './component/tree.vue'
import { getShiftList, attendanceShiftAdd, getDeptListNew, salaryRuleUserBind, getSalaryRuleUserBindList } from '@/api/system/commonAttendance';
import { ref, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import {
  Plus,
  Minus
} from '@element-plus/icons-vue'
const message = useMessage()

/**
* 路由对象
*/
const route = useRoute();
/**
* 数据部分
*/
const queryFormRef = ref()
const list = ref([])
const total = ref(0);
const queryParams = reactive({
  name: "",
  pageNo: 1,
  pageSize: 10,
  type: 2
})
const handleSearch = () => {
  queryParams.pageNo = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {

  queryFormRef.value.resetFields()

  handleSearch()
}
const getList = async () => {
  console.log("触发 getList")
  const res = await getShiftList(queryParams);
  list.value = res.data.list.map(e => {
    return {
      ...e,
      createUser: e.owner.name
    }
  })

  for (let i = 0; i < list.value.length; i++) {

    for (let j = 0; j < list.value[i].shiftTimeRanges.length; j++) {

      if (list.value[i].shiftTimeRanges[j].efficientStratTime) {

        list.value[i].shiftTimeRanges[j].efficientTimeRange = [list.value[i].shiftTimeRanges[j].efficientStratTime, list.value[i].shiftTimeRanges[j].efficientEndTime]
      } else {
        list.value[i].shiftTimeRanges[j].efficientTimeRange = []
      }


    }
  }


  console.log("触发 list.value", list.value)



  total.value = res.data.total;
}

onMounted(() => {

  console.log("onMounted")
  getList()
})




const editFormRef = ref()




const rules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
  ],
};


const title = ref("")
const editVisible = ref(false)

const editForm = ref({
  id: null,
  name: "",
  shiftTimes: [],
  type: 2,
})

const shiftTimesAdd = () => {
  let rangeIndex = 1
  if (editForm.value.shiftTimes.length == 0 || editForm.value.shiftTimes.length == 1) {
    rangeIndex = 1
  } else if (editForm.value.shiftTimes.length == 2 || editForm.value.shiftTimes.length == 3) {
    rangeIndex = 2
  } else if (editForm.value.shiftTimes.length == 4 || editForm.value.shiftTimes.length == 5) {
    rangeIndex = 3
  } else if (editForm.value.shiftTimes.length == 6 || editForm.value.shiftTimes.length == 7) {
    rangeIndex = 4
  }

  let rangeType = 0
  if (editForm.value.shiftTimes.length == 0) {
    rangeType = 0
  } else if (editForm.value.shiftTimes.length == 1) {
    rangeType = 1
  } else if (editForm.value.shiftTimes.length == 2) {
    rangeType = 0
  } else if (editForm.value.shiftTimes.length == 3) {
    rangeType = 1
  } else if (editForm.value.shiftTimes.length == 4) {
    rangeType = 0
  } else if (editForm.value.shiftTimes.length == 5) {
    rangeType = 1
  } else if (editForm.value.shiftTimes.length == 6) {
    rangeType = 0
  } else if (editForm.value.shiftTimes.length == 7) {
    rangeType = 1
  }


  let description = "第" + rangeIndex + "次" + (rangeType == 0 ? "上班" : "下班")




  editForm.value.shiftTimes.push({
    description: description,
    rangeIndex: rangeIndex,
    startTime: null,
    efficientStratTime: '',
    efficientEndTime: "",
    type: 2,
    rangeType: rangeType,
    efficientTimeRange: []
  })

}


const deepClone = (obj) => {
  // 如果是 null 或非对象类型，直接返回
  if (obj === null || typeof obj !== 'object') return obj

  // 如果是数组，递归拷贝数组中的每个元素
  if (Array.isArray(obj)) return obj.map(deepClone)

  // 如果是对象，递归拷贝对象的每个属性
  const newObj = {}
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      newObj[key] = deepClone(obj[key])
    }
  }
  return newObj
}

const handleEdit = (row) => {
  if (row) {
    console.log("handleEdit", row)
    editForm.value = deepClone(row)
    editForm.value.shiftTimes = editForm.value.shiftTimeRanges
    delete editForm.value.shiftTimeRanges
    editVisible.value = true
    title.value = "编辑考勤组信息"
  } else {
    editForm.value = {
      id: null,
      name: "",
      shiftTimes: [],
      type: 2,
    }
    editVisible.value = true
    title.value = "新增考勤组信息"

  }

}

const editSubmit = async () => {
  console.log("editSubmit", editForm.value)
  for (let i = 0; i < editForm.value.shiftTimes.length; i++) {
    if (!editForm.value.shiftTimes[i].startTime) {
      message.error("请选择工作日时间")
      return

    }

    if (!editForm.value.shiftTimes[i].efficientStratTime) {
      message.error("请选择打卡时间范围")
      return

    }
  }

  await editFormRef.value.validate((valid, fields) => {
    if (valid) {
      console.log("editAttendanceDetailForm=", editForm.value)


      editForm.value.type = 2
      attendanceShiftAdd(editForm.value).then(res => {
        if (res.code == 0) {
          message.success("提交成功")
          editVisible.value = false
          getList()
        } else {
          message.error("提交失败")
        }
      })




    } else {
      console.log('error submit!', fields)
    }
  })



}

// getDeptListNew,salaryRuleUserBind,getSalaryRuleUserBindList






const configTree = ref()

const handleConfig = async (row) => {
  console.log("handleConfig", row)

  configTree.value.show(2, row.id)

}

const handleTimeRangeChange = (row) => {

  if (row.efficientTimeRange && row.efficientTimeRange.length > 0) {
    row.efficientStratTime = row.efficientTimeRange[0]
    row.efficientEndTime = row.efficientTimeRange[1]
  } else {
    row.efficientStratTime = ''
    row.efficientEndTime = ''
  }

}


</script>

<style scoped lang='less'>
.app-main {
  padding: 20px;
  border-radius: 10px;
  background-color: #fff;
  min-height: calc(100vh - 150px);
}

.option-btns {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: center;
}

.input-box {
  width: 300px;
}

.timeBox {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.time-input {
  width: 250px;
  margin-right: 10px;
}










.dept-tree-container {
  padding: 20px;
  max-width: 800px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.dept-name {
  font-weight: 600;
  color: #333;
}

.dept-info {
  margin-left: 20px;
  color: #666;
}

.user-count {
  margin-right: 15px;
}

.leaders {
  font-size: 12px;
  color: #999;
}

:deep(.el-tree-node__content) {
  height: 40px;
}
</style>