<template>
  <div>
    <el-input
      v-model="inputValue"
      class="searchBox"
      placeholder="搜索联系人和表单"
      :prefix-icon="Search"
      :class="{ focusBox: isOpen || isFocus }"
      @focus="inputFocus"
      @blur="inputBlur"
      @input="inputChange"
      clearable
      ref="inputRef"
    />
    <el-card class="oaCard" v-if="isOpen" ref="refBox">
      <div v-for="(item, index) in listData" :key="index" class="forBox" @click="handleMsg(item)">
        <avatar :size="30" :name="item.nickname" :src="item.avatar" class="search-avatar" />
        <div class="rightInfo">
          <p class="name">{{ item.nickname }}</p>
          <p>{{ item.postName?.join('、') }}</p>
          <p>{{ item.deptAllName?.join('、') }}</p>
        </div>
      </div>
      <p v-if="listData.length == 0 && !loading" class="tipP">暂无搜索结果</p>
      <p v-if="listData.length == 0 && loading" class="tipP">正在加载中...</p>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { useMyStore } from '@/store/modules/jump'
import { Search } from '@element-plus/icons-vue'
import { userPageAll } from '@/api/org'

const router = useRouter()

const inputValue = ref('')
const isFocus = ref(false)
const inputFocus = () => {
  isFocus.value = true
}
const inputBlur = () => {
  isFocus.value = false
}

const isOpen = ref(false)

interface ListItem {
  nickname: string
  avatar: string
  deptAllName: string[]
  postName: string[]
}

const listData = ref<ListItem[]>([])
const refBox = ref(null)
const inputRef = ref(null)
const loading = ref(false)
const inputChange = async (e) => {
  isFocus.value = true
  isOpen.value = true
  loading.value = true
  if (e.trim()) {
    try {
      const res = await userPageAll({ nickname: e.trim(), pageSize: 100 })
      listData.value = res.data.list.map((item) => {
        if (item.type === 'flow') {
          return {
            ...item.formInfo,
            type: 'flow',
            nickname: item.formInfo.formName,
            avatar: '',
            deptAllName: [item.formInfo.groupName],
            postName: []
          }
        }
        return item
      })
    } finally {
      loading.value = false
    }
  } else {
    isOpen.value = false
    listData.value = []
  }
}

const hideBox = (e) => {
  if (
    refBox.value &&
    !refBox.value.$el.contains(e.target) &&
    inputRef.value &&
    !inputRef.value.$el.contains(e.target)
  ) {
    clearAll()
  }
}
const clearAll = () => {
  isOpen.value = false
  isFocus.value = false
  inputValue.value = ''
}
const handleMsg = async (item) => {
  try {
    if (item.type === 'flow') {
      localStorage.setItem('searchFlowInfo', JSON.stringify(item))
      await router.push('/home')
      router.go(0) // 强制刷新页面
    } else {
      useMyStore().updateValue(item)
      await router.push('/index')
    }
    clearAll()
  } catch (error) {
    //清除本地存储
    localStorage.removeItem('searchFlowInfo')
    console.error('跳转时发生错误:', error)
  }
}

watch(isOpen, (newPath, oldPath) => {
  if (newPath) {
    document.addEventListener('click', hideBox)
  } else {
    document.removeEventListener('click', hideBox)
  }
})

onUnmounted(() => {
  document.removeEventListener('click', hideBox)
})
</script>
<style lang="less" scoped>
.searchBox {
  width: 220px;
  height: 28px;
  --el-input-hover-border: none;
  --el-input-border: none;
  --el-input-focus-border: none;
  --el-input-border-color: transparent;
  --el-input-bg-color: rgba(255, 255, 255, 0.15);
  --el-input-placeholder-color: rgba(255, 255, 255, 0.6);
  --el-input-icon-color: rgba(255, 255, 255, 0.6);
  --el-input-hover-border-color: transparent;
  --el-input-focus-border-color: transparent;
  --el-input-text-color: #fff;
}

.searchBox:hover {
  --el-input-bg-color: rgba(255, 255, 255, 0.2);
}

.focusBox {
  width: 450px;
}

.oaCard {
  position: fixed;
  z-index: 100;
  width: 450px;
  border-radius: 2px !important;
  min-height: 300px;
  max-height: 600px;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12) !important;
  overflow-y: auto;
}

.forBox {
  padding: 10px;
  margin: -10px -10px 10px;
  cursor: pointer;
  display: flex;
  border-bottom: 1px solid #ebeef5;
}

.forBox:hover {
  background: #f1f2f5;
  border-radius: 6px;
}

.tipP {
  text-align: center;
  font-size: 14px;
  color: #7a7780;
}

:deep(.search-avatar) {
  display: inline;
}

:deep(.search-avatar .name) {
  display: none;
}

:deep(.search-avatar .a-img > div) {
  font-size: 12px;
}

.rightInfo {
  margin-left: 10px;

  p {
    margin: 0;
    font-size: 12px;
    color: #9a9b9f;
    // margin-bottom: 2px;
  }

  p:nth-child(2) {
    color: #7e8183;
  }

  .name {
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
  }
}
</style>
