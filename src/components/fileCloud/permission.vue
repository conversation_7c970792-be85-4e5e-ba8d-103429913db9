<template>
  <Dialog title="权限管理" v-model="dialogVisible" width="500">
    <div class="top">
      <span>所有协作者</span>
      <span @click="handleAdd">
        <Plus />添加协作者
      </span>
    </div>
    <div class="container">
      <!-- <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
    <el-checkbox-group v-model="checkedValues" @change="handleCheckedCitiesChange">
      <el-checkbox v-for="(item, index) in checkList" :key="index" :label="item.userName"> -->
      <div v-for="(item, index) in checkList" :key="index" class="forBox">
        <div class="checkItem">
          <div class="left">
            <avatar :name="item.userName ? item.userName.slice(-1) : ''" :src="''" :size="32" :title="item.userName">
            </avatar>
            <div class="nameRight">
              <span>{{ item.userName }}</span>
              <!-- <span>由「{{ item.userName }}」添加</span> -->
            </div>
          </div>
          <div class="right" @click.prevent="handleConfig">
            <!-- <p v-if="item.permission == 1" class="notEvent">可管理</p> -->
            <el-dropdown @command="(command) => handleCommand(command, item)" trigger="click">
              <span class="dropSpan">
                {{ item.permission == 1 ? '可管理' : item.permission == 2 ? '可编辑' : '可查看/下载' }}<arrow-down />
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="1" :icon="Setting" :class="{ 'active-item': item.permission == 1 }">
                    <div class="itemDiv">
                      <p>可管理</p>
                      <p>可编辑/上传/下载/删除</p>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="2" :icon="Edit" :class="{ 'active-item': item.permission == 2 }">
                    <div class="itemDiv">
                      <p>可编辑</p>
                      <p>可编辑/上传/下载</p>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="3" :icon="Download" :class="{ 'active-item': item.permission == 3 }">
                    <div class="itemDiv">
                      <p>可查看/下载</p>
                      <p>可下载</p>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="4" :icon="Delete" class="special" v-if="item.removeFlag">
                    <div class="itemDiv">
                      <p>移除</p>
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
      <!-- </el-checkbox>
    </el-checkbox-group> -->
    </div>
  </Dialog>
  <org-picker title="添加人员" ref="orgPicker" multiple type="user" :selected="[]" @ok="selected"
    :tenantId="rowObj.tenantId" />
  <ccc @success="userSuccess" ref="userPermission"></ccc>
</template>
<script setup lang="ts">
import { Plus, Setting, Edit, Download, ArrowDown, Delete } from '@element-plus/icons-vue'
import { fileCloudApi } from '@/api/fileCloud/list'
import OrgPicker from '@/components/common/OrgPicker.vue'
import ccc from './userConfig.vue'
const { t } = useI18n()
const message = useMessage()
const dialogVisible = ref(false)

const rowObj = ref({
  id: undefined,
  tenantId: undefined,
  parentId: undefined,
  type: undefined
})
const open = async (item) => {
  rowObj.value = item
  console.log(item)
  checkList.value = []
  dialogVisible.value = true
  getList()
}
const checkAll = ref(false)
const isIndeterminate = ref(false)
const checkedValues = ref<string[]>([])
const checkList = ref([])

const handleCheckAllChange = (val: boolean) => {
  checkedValues.value = val ? checkList.value.map(item => item.userName) : []
  isIndeterminate.value = false
}
const handleCheckedCitiesChange = (value: string[]) => {
  const checkedCount = value.length
  checkAll.value = checkedCount === checkList.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < checkList.value.length
}

const handleConfig = () => {

}
const getList = async () => {
  try {
    const res = await fileCloudApi.permissionList({
      cloudId: rowObj.value.id,
      type: rowObj.value.type ? rowObj.value.type : 1,
      tenantId: rowObj.value.tenantId
    })
    checkList.value = res
  } finally {

  }
}
// 更多下拉
const handleCommand = (i, item) => {
  if (i == 4) {
    deleteUser(item)
  } else {
    handlePermission(i, item)
  }
}
const deleteUser = async (item) => {
  try {
    await fileCloudApi.permissionDelete(item.id)
    message.success('操作成功')
    getList()
  } finally {

  }
}
const handlePermission = async (i, item) => {
  console.log(item)
  const obj = {
    id: item.id,
    cloudId: item.cloudId,
    userId: item.userId,
    type: item.type,
    tenantId: rowObj.value.tenantId,
    permission: Number(i),
  }
  try {
    await fileCloudApi.permissionUpdate(obj)
    message.success('修改成功')
    getList()
  } finally {

  }
}

const orgPicker = ref()
const handleAdd = () => {
  orgPicker.value.show()
}
const userPermission = ref()
const selected = (users: any) => {
  const bValues = new Set(checkList.value.map(item => String(item.userId)))
  const newArray = users.filter(item => !bValues.has(String(item.id)))
  if (newArray.length == 0) return message.warning('暂无新增成员')
  userPermission.value.open(newArray, rowObj.value)
}
const userSuccess = (e) => {
  getList()
}





defineExpose({ open })
const emit = defineEmits(['success'])





</script>
<style scoped lang="less">
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span:nth-child(1) {
    color: #606266;
  }

  span:nth-child(2) {
    display: flex;

    svg {
      width: 14px;
      margin-right: 4px;
    }

    color: #3370FF;
  }
}

:deep(.el-checkbox) {
  width: 100%;
  margin-bottom: 20px;
}

:deep(.el-checkbox__label) {
  width: 100%;
}

:deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
  color: #303133;
}

.checkItem {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;

    :deep(.avatar) {
      margin-right: 12px;
    }

    :deep(.avatar .a-img > div) {
      border-radius: 4px;
      font-size: 14px;
      background: #3370FF;
    }

    :deep(.avatar .name) {
      display: none;
    }

    .nameRight {
      span {
        display: block;

      }

      span:nth-child(1) {
        margin-bottom: 3px;
      }

      span:nth-child(2) {
        font-size: 12px;
        color: #A2A3A5;
      }
    }
  }

  .right {
    height: 32px;
    align-items: center;
    display: flex;
    justify-content: end;
    margin-left: 100px;

    .dropSpan {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      cursor: pointer;

      svg {
        width: 14px;
        margin-left: 4px;
      }
    }

    .dropSpan:hover::before {
      content: '';
      position: absolute;
      left: -8px;
      right: -8px;
      bottom: -6px;
      top: -6px;
      background: #F3F4F7;
      border-radius: 4px;
      z-index: -1;
    }
  }
}

.itemDiv {
  display: block;
  width: 120px;

  // margin-bottom: 20px;
  p {
    margin: 0;
    padding: 0;
  }

  p:nth-child(2) {
    position: absolute;
    font-size: 12px;
  }
}


:deep(.el-dropdown-menu__item) {
  padding-bottom: 30px;
}

.forBox {
  margin-bottom: 10px;

}

.container {
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  padding: 16px 16px 6px;
  box-sizing: border-box;
  margin-top: 20px;
}

:deep(.special) {
  // icon{
  //   color:red;
  // }
  // color:red;
  padding-bottom: 10px;
}

:deep(.el-dropdown-menu__item:not(.is-disabled):focus) {
  background: #F3F4F7;
  color: #303133;
}

:deep(.active-item) {
  // background: #F3F4F7;
  // color: #303133;
}
:deep(.el-dropdown-menu__item i){
  font-size: 16px;
}
</style>