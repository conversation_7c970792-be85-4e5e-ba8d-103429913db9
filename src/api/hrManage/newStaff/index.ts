import request from '@/config/axios'

// 新成员申请 VO
export interface StaffApplyVO {
  id: number // ID
  username: string // 申请加入名称
  mobile: string // 手机号码
  deptId: number // 申请加入部门ID
  applyReason: string // 申请理由
  inviterId: number // 邀请人ID
  inviterName: string // 邀请人姓名
  approveStatus: number // 审批状态（-1_未审批；0_同意；1_拒绝）
}

// 新成员申请 API
export const StaffApplyApi = {
  // 查询新成员申请分页
  getStaffApplyPage: async (params: any) => {
    return await request.get({ url: `/oa/staff-apply/page`, params })
  },

  // 查询新成员申请详情
  getStaffApply: async (id: number) => {
    return await request.get({ url: `/oa/staff-apply/get?id=` + id })
  },

  // 新增新成员申请
  createStaffApply: async (data: StaffApplyVO) => {
    return await request.post({ url: `/oa/staff-apply/create`, data })
  },

  // 修改新成员申请
  updateStaffApply: async (data: StaffApplyVO) => {
    return await request.put({ url: `/oa/staff-apply/update`, data })
  },

  // 删除新成员申请
  deleteStaffApply: async (id: number) => {
    return await request.delete({ url: `/oa/staff-apply/delete?id=` + id })
  },

  // 导出新成员申请 Excel
  exportStaffApply: async (params) => {
    return await request.download({ url: `/oa/staff-apply/export-excel`, params })
  },
}
// 链接
export const invite = async (params) => {
  return await request.get({
    url: `oa/staff-apply/invite`,
    params
  })
}
// 二维码
export const qrcode = async (params) => {
  return await request.get({
    url: `oa/staff-apply/qrcode`,
    params
  })
}
// 审核列表
export const approve = async (data: any) => {
  return await request.post2({
    url: `oa/staff-apply/approve`,
    data
  })
}

//审核前人资对员工进行完善合同配置信息
export const completeList = async () => {
  return await request.get({
    url: `system/custom-table/config/complete`,
  })
}
//获取合同模板列表
export const getContractTemplateList = async () => {
  return await request.get({
    url: `/system/contract/template/list`,
  })
}
