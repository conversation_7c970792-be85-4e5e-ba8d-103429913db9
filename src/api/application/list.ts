import request from '@/config/axios'

export const applicationApi = {
  //授权 可见范围下拉列表
  applicationScope: async () => {
    return await request.get({ url: `/system/application/scope/list` })
  },
  // 应用列表接口
  scopeList: async (params) => {
    return await request.get({ url: `/system/application/list`,params })
  },
  // 创建-更新应用接口
  createApplication: async (data) => {
    return await request.post({ url: `/system/application/create`,data })
  },
  // 应用详情
  getApplication: async (params) => {
    return await request.get({ url: `/system/application/get`,params })
  },
  // 删除应用
  deleteApplication: async (id: number) => {
    return await request.delete({ url: `/system/application/delete?id=` + id  })
  },

  //分类 下拉列表 /banban/admin-api
  //type 1应用中心 2生活中心
  lifeTypeList: async (params: {type: number}) => {
    return await request.get({ url: `/system/life/center/type/list`, params })
  }, 
  // 分类分页列表接口
  lifeTypeListPage: async (params) => {
    return await request.get({ url: `/system/life/center/type/page`,params })
  },
  // 创建-分类接口
  createType: async (data) => {
    return await request.post({ url: `/system/life/center/type/add`,data })
  },
  // 编辑-分类接口
  updateType: async (data) => {
    return await request.post({ url: `/system/life/center/type/update`,data })
  },
    // 删除应用
    deleteType: async (id: number) => {
      return await request.delete({ url: `/system/life/center/type/delete?id=` + id  })
    },
}