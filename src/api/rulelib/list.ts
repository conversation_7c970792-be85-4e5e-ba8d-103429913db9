import request from '@/config/axios'

export const rulelibApi = {
  // //团队文件 根目录
  // rootPage: async (params) => {
  //   return await request.get({ url: `/system/cloud-drive/rootpage`,params })
  // },
  // create: async (data) => {
  //   return await request.post({ url: `/system/cloud-drive/create`,data })
  // },
  // fileDelete: async (id) => {
  //   return await request.delete({ url: `/system/cloud-file/delete?id=` + id })
  // },
  // 制度文库分类树形列表
  getRuleLibClassTree: async (params) => {
    return await request.get({ url: `/system/ruleLib/class/tree`,params })
  },
  // 新增制度文库分类
   create: async (data) => {
    return await request.post({ url: `/system/ruleLib/class`,data })
  },
  // 编辑制度文库分类
  editTreeItemName: async (data) => {
    return await request.put({ url: `/system/ruleLib/class`,data })
  },
  // 删除制度文库分类
  delTreeItem: async (id) => {
    return await request.delete({ url: `/system/ruleLib/class/` + id })
  },



  // 获取制度文库分类列表  制度文库分页列表
  getRuleLibFilePage: async (params) => {
    return await request.get({ url: `/system/ruleLib/file/page`,params })
  },

  //新增制度文件 
  ruleLibFileAdd: async (data) => {
    return await request.post({ url: `/system/ruleLib/file `,data })
  },
  // 编辑制度文件
  ruleLibFileEdit: async (data) => {
    return await request.post({ url: `/system/ruleLib/file `,data })
  },
  // 删除制度文件
  ruleLibFileDel: async (id) => {
    return await request.delete({ url: `/system/ruleLib/file/` + id })
  },
  // 制度文库重命名 
  ruleLibFileRename: async (data) => {
    return await request.post({ url: `/system/ruleLib/file/rename `,data })
  },
  // 获取制度文件权限分页列表 
  getRuleLibPermPage: async (params) => {
    return await request.get({ url: `/system/ruleLib/perm/page`,params })
  },
  // 权限保存
  permissionUpdate: async (data) => {
    return await request.post({ url: `/system/ruleLib/perm/save`,data })
  },
  // 权限删除
  permissionDelete: async (id) => {
    return await request.delete({ url: `/system/ruleLib/perm/` + id })
  },
  // 获取当前用户权限 
  getMyPermission: async (params) => {
    return await request.get({ url: `/system/ruleLib/perm/getme`,params })
  },
}