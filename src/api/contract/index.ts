import request from '@/config/axios'

export interface ContractVO {
  id: number | undefined
  createTime: Date
  name: string
  userId: number
  signTime: Date
  signContractUrl: string
  contractNo: string
  status: number
  contractType: number
  tenantId: number
  contractFileUrl: string
  contractPersonCode: string
  enterpriseParams: string
}

// 查询合同列表
export const getContractPage = async (params: PageParam) => {
  return await request.get({ url: '/system/contract/page', params })
}

// 右上角个人合同开关设置
export const setPersonalContractSwitch = async (params: { isOpenContract: number }) => {
  return await request.post({ url: '/system/contract/setting', data: params })
}
