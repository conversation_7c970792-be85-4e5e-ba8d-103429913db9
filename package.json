{"name": "banban", "version": "2.2.1", "description": "诺鑫办办", "author": "诺鑫", "private": false, "main": "electron/main.js", "scripts": {"i": "pnpm install", "dev": "vite --mode dev", "test": "vite --mode test", "prod": "vite --mode prod", "ts:check": "vue-tsc --noEmit", "build:local-dev": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode local-dev", "build:dev": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode dev", "build:test": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode test", "build:stage": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode stage", "build:prod": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode prod", "serve:dev": "vite preview --mode dev", "serve:prod": "vite preview --mode prod", "preview": "pnpm build:local-dev && vite preview", "clean": "npx rimraf node_modules", "clean:cache": "npx rimraf node_modules/.cache", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:style": "stylelint --fix \"./src/**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ", "electron": "electron .", "electron:build": "electron-builder --win", "electron-mac": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode prod && electron-builder --mac", "electron-win-test": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode test && electron-builder --win", "electron-win": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode prod && electron-builder --win", "electron-win32": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode prod && electron-builder --win --ia32", "electron-win64": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode prod && electron-builder --win --x64", "electron-linux": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode prod && electron-builder --linux"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "^2.1.0", "@form-create/designer": "^3.1.3", "@form-create/element-ui": "^3.1.24", "@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/list": "^6.1.11", "@fullcalendar/timegrid": "^6.1.11", "@fullcalendar/vue3": "^6.1.11", "@iconify/iconify": "^3.1.1", "@tinymce/tinymce-vue": "^5.1.0", "@vant/area-data": "^1.4.0", "@vant/compat": "^1.0.0", "@videojs-player/vue": "^1.0.0", "@vue-office/excel": "^1.7.14", "@vue/compiler-sfc": "^3.3.4", "@vuemap/vue-amap": "^2.0.5", "@vueuse/core": "^10.9.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.10", "@zxcvbn-ts/core": "^3.0.4", "@zxing/library": "^0.19.2", "animate.css": "^4.1.1", "axios": "^1.6.7", "benz-amr-recorder": "^1.1.5", "bpmn-js-token-simulation": "^0.10.0", "camunda-bpmn-moddle": "^7.0.1", "clipboard": "^2.0.11", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "diagram-js": "^12.8.0", "driver.js": "^1.3.1", "echarts": "^5.5.0", "echarts-wordcloud": "^2.1.0", "electron-log": "^5.1.7", "electron-updater": "^6.2.1", "element-plus": "^2.8.8", "fast-xml-parser": "^4.3.2", "highlight.js": "^11.9.0", "html2canvas": "^1.4.1", "jsencrypt": "^3.3.2", "less": "^4.1.3", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "min-dash": "^4.1.1", "mitt": "^3.0.1", "moment": "^2.29.4", "monaco-editor": "^0.38.0", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.0", "qrcode": "^1.5.3", "qrcode.vue": "^3.4.0", "qs": "^6.11.2", "s": "^1.0.0", "signature_pad": "^4.1.5", "steady-xml": "^0.1.0", "tinymce": "^6.0.3", "url": "^0.11.3", "vant": "^4.1.0", "video.js": "^7.21.5", "vue": "3.4.20", "vue-cal": "^4.8.1", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.14.10", "vue-dompurify-html": "^4.1.4", "vue-i18n": "9.9.1", "vue-router": "^4.3.0", "vue-types": "^5.1.1", "vuedraggable": "^4.1.0", "vuex": "^4.1.0", "wangeditor": "^4.7.15", "web-storage-cache": "^1.1.1", "xml-js": "^1.6.11"}, "devDependencies": {"@commitlint/cli": "^19.0.1", "@commitlint/config-conventional": "^19.0.0", "@iconify/json": "^2.2.187", "@intlify/unplugin-vue-i18n": "^2.0.0", "@purge-icons/generated": "^0.9.0", "@types/lodash-es": "^4.17.12", "@types/node": "^20.11.21", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.12", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@unocss/eslint-config": "^0.57.4", "@unocss/transformer-variant-group": "^0.58.5", "@vitejs/plugin-legacy": "^5.3.1", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.17", "bpmn-js": "8.9.0", "bpmn-js-properties-panel": "0.46.0", "consola": "^3.2.3", "cross-env": "^7.0.3", "electron": "^31.1.0", "electron-builder": "^24.13.3", "electron-devtools-installer": "^3.2.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.22.0", "lint-staged": "^15.2.2", "postcss": "^8.4.35", "postcss-html": "^1.6.0", "postcss-scss": "^4.0.9", "prettier": "^3.2.5", "rimraf": "^5.0.5", "rollup": "^4.12.0", "@rollup/plugin-typescript": "^9.0.1", "sass": "^1.83.1", "stylelint": "^16.2.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended": "^14.0.0", "stylelint-config-standard": "^36.0.0", "stylelint-order": "^6.0.4", "terser": "^5.28.1", "typescript": "5.3.3", "unocss": "^0.58.5", "unplugin-auto-import": "^0.16.7", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.25.2", "vite": "5.1.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-ejs": "^1.7.0", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-top-level-await": "^1.3.1", "vue-cli-plugin-style-resources-loader": "^0.1.5", "vue-eslint-parser": "^9.3.2", "vue-tsc": "^1.8.27"}, "resolutions": {"@rollup/pluginutils/@rollup/plugin-typescript": "8.5.0"}, "pnpm": {"overrides": {"@swc/core": "1.6.0"}}, "license": "MIT", "repository": {"type": "git", "url": "git+http://gitlab.dinggehuo.com/banban-oa/oa-web"}, "bugs": {"url": "http://gitlab.dinggehuo.com/banban-oa/oa-web/issues"}, "homepage": "http://gitlab.dinggehuo.com/banban-oa/oa-web", "packageManager": "pnpm@8.6.0", "engines": {"node": ">= 16.0.0", "pnpm": ">=8.6.0"}, "build": {"productName": "诺鑫办办", "appId": "com.nuoxin.banban", "copyright": "oa.nxbanban © 2024", "compression": "maximum", "asar": true, "publish": [{"provider": "generic", "url": "https://oa.nxbanban.com/banban/version/"}], "files": ["dist-prod/**/*", "electron/**/*"], "directories": {"output": "release/"}, "electronDownload": {"mirror": "https://cdn.npmmirror.com/binaries/electron/", "cache": "/Users/<USER>/Documents/data/electron/"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "perMachine": true, "deleteAppDataOnUninstall": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "诺鑫办办"}, "win": {"artifactName": "${productName}-v${version}-${platform}-setup.${ext}", "target": ["nsis"], "icon": "./public/logoPC.png"}, "mac": {"icon": "./public/logoPC.png", "category": "public.app-category.utilities", "artifactName": "${productName}-v${version}-${platform}-setup.${ext}"}, "linux": {"target": ["AppImage", "deb"], "icon": "./public/logoPC.png", "artifactName": "${productName}-v${version}-${platform}-setup.${ext}", "category": "Utility", "maintainer": "zhusheng <<EMAIL>>"}}}